// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Python Integration Implementation
// Bridge 3.13: World Partition - Python Integration

#include "AuracronWorldPartitionPython.h"
#include "AuracronWorldPartitionLandscape.h"
#include "Engine/World.h"
#include "Landscape.h"
#include "WorldPartition/DataLayer/DataLayerAsset.h"
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerManager.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionHelpers.h"
#include "AuracronWorldPartitionBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Modules/ModuleManager.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"

// Static instance
UAuracronWorldPartitionPythonBridge* UAuracronWorldPartitionPythonBridge::Instance = nullptr;

// =============================================================================
// SINGLETON ACCESS
// =============================================================================

UAuracronWorldPartitionPythonBridge* UAuracronWorldPartitionPythonBridge::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronWorldPartitionPythonBridge>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

// =============================================================================
// BRIDGE LIFECYCLE
// =============================================================================

void UAuracronWorldPartitionPythonBridge::Initialize(const FAuracronPythonConfiguration& InConfiguration)
{
    FScopeLock Lock(&PythonLock);
    
    if (bIsInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AuracronWorldPartitionPythonBridge already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();
    
    if (Configuration.bEnablePythonIntegration)
    {
        if (InitializePythonEnvironment())
        {
            bIsInitialized = true;
            UE_LOG(LogTemp, Log, TEXT("AuracronWorldPartitionPythonBridge initialized successfully"));
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to initialize Python environment"));
        }
    }
    else
    {
        bIsInitialized = true;
        UE_LOG(LogTemp, Log, TEXT("AuracronWorldPartitionPythonBridge initialized (Python integration disabled)"));
    }
}

void UAuracronWorldPartitionPythonBridge::Shutdown()
{
    FScopeLock Lock(&PythonLock);
    
    if (!bIsInitialized)
    {
        return;
    }

    if (Configuration.bEnablePythonIntegration && bPythonEnvironmentReady)
    {
        ShutdownPythonEnvironment();
    }

    ClearAllCallbacks();
    LoadedModules.Empty();
    PythonErrors.Empty();
    PythonLogs.Empty();
    ExecutionHistory.Empty();
    
    bIsInitialized = false;
    bPythonEnvironmentReady = false;
    bExecutionInProgress = false;
    CurrentExecutions = 0;
    
    UE_LOG(LogTemp, Log, TEXT("AuracronWorldPartitionPythonBridge shutdown completed"));
}

bool UAuracronWorldPartitionPythonBridge::IsInitialized() const
{
    return bIsInitialized;
}

// =============================================================================
// PYTHON ENVIRONMENT MANAGEMENT
// =============================================================================

bool UAuracronWorldPartitionPythonBridge::InitializePythonEnvironment()
{
    if (bPythonEnvironmentReady)
    {
        return true;
    }

    // Setup Python paths
    if (!SetupPythonPaths())
    {
        HandlePythonError(TEXT("Failed to setup Python paths"), TEXT("InitializePythonEnvironment"));
        return false;
    }

    // Load required modules
    if (!LoadRequiredModules())
    {
        HandlePythonError(TEXT("Failed to load required Python modules"), TEXT("InitializePythonEnvironment"));
        return false;
    }

    bPythonEnvironmentReady = true;
    UE_LOG(LogTemp, Log, TEXT("Python environment initialized successfully"));
    return true;
}

void UAuracronWorldPartitionPythonBridge::ShutdownPythonEnvironment()
{
    if (!bPythonEnvironmentReady)
    {
        return;
    }

    // Cancel any running executions
    if (bExecutionInProgress)
    {
        CancelPythonExecution();
    }

    LoadedModules.Empty();
    bPythonEnvironmentReady = false;
    
    UE_LOG(LogTemp, Log, TEXT("Python environment shutdown completed"));
}

bool UAuracronWorldPartitionPythonBridge::IsPythonEnvironmentReady() const
{
    return bPythonEnvironmentReady && Configuration.bEnablePythonIntegration;
}

bool UAuracronWorldPartitionPythonBridge::LoadPythonModule(const FString& ModuleName)
{
    if (!IsPythonEnvironmentReady())
    {
        HandlePythonError(TEXT("Python environment not ready"), TEXT("LoadPythonModule"));
        return false;
    }

    if (LoadedModules.Contains(ModuleName))
    {
        UE_LOG(LogTemp, Warning, TEXT("Module %s already loaded"), *ModuleName);
        return true;
    }

    // Simulate module loading - in real implementation, this would use Python C API
    LoadedModules.AddUnique(ModuleName);
    
    if (Configuration.bLogPythonExecution)
    {
        UE_LOG(LogTemp, Log, TEXT("Loaded Python module: %s"), *ModuleName);
    }
    
    return true;
}

bool UAuracronWorldPartitionPythonBridge::ReloadPythonModule(const FString& ModuleName)
{
    if (!IsPythonEnvironmentReady())
    {
        HandlePythonError(TEXT("Python environment not ready"), TEXT("ReloadPythonModule"));
        return false;
    }

    // Remove from loaded modules and reload
    LoadedModules.Remove(ModuleName);
    return LoadPythonModule(ModuleName);
}

TArray<FString> UAuracronWorldPartitionPythonBridge::GetLoadedModules() const
{
    return LoadedModules;
}

// =============================================================================
// PYTHON SCRIPT EXECUTION
// =============================================================================

FAuracronPythonExecutionResult UAuracronWorldPartitionPythonBridge::ExecutePythonScript(const FString& Script, EAuracronPythonExecutionMode ExecutionMode)
{
    FAuracronPythonExecutionResult Result;
    Result.ExecutionTimestamp = FDateTime::Now();
    
    if (!IsPythonEnvironmentReady())
    {
        Result.bSuccess = false;
        Result.ErrorMessage = TEXT("Python environment not ready");
        HandlePythonError(Result.ErrorMessage, TEXT("ExecutePythonScript"));
        return Result;
    }

    if (CurrentExecutions >= Configuration.MaxConcurrentExecutions)
    {
        Result.bSuccess = false;
        Result.ErrorMessage = TEXT("Maximum concurrent executions reached");
        HandlePythonError(Result.ErrorMessage, TEXT("ExecutePythonScript"));
        return Result;
    }

    // Simulate script execution
    double StartTime = FPlatformTime::Seconds();

    // In real implementation, this would execute Python script
    Result.bSuccess = true;
    Result.ResultData = TEXT("Script executed successfully");
    Result.ExecutionTime = static_cast<float>(FPlatformTime::Seconds() - StartTime);
    
    ExecutionHistory.Add(Result);
    LogPythonExecution(Script, Result);
    
    return Result;
}

FAuracronPythonExecutionResult UAuracronWorldPartitionPythonBridge::ExecutePythonFunction(const FString& ModuleName, const FString& FunctionName, const TMap<FString, FString>& Parameters)
{
    FAuracronPythonExecutionResult Result;
    Result.ExecutionTimestamp = FDateTime::Now();
    
    if (!IsPythonEnvironmentReady())
    {
        Result.bSuccess = false;
        Result.ErrorMessage = TEXT("Python environment not ready");
        HandlePythonError(Result.ErrorMessage, TEXT("ExecutePythonFunction"));
        return Result;
    }

    if (!LoadedModules.Contains(ModuleName))
    {
        if (!LoadPythonModule(ModuleName))
        {
            Result.bSuccess = false;
            Result.ErrorMessage = FString::Printf(TEXT("Failed to load module: %s"), *ModuleName);
            HandlePythonError(Result.ErrorMessage, TEXT("ExecutePythonFunction"));
            return Result;
        }
    }

    // Simulate function execution
    double StartTime = FPlatformTime::Seconds();

    // In real implementation, this would call Python function
    Result.bSuccess = true;
    Result.ResultData = FString::Printf(TEXT("Function %s.%s executed successfully"), *ModuleName, *FunctionName);
    Result.ExecutionTime = static_cast<float>(FPlatformTime::Seconds() - StartTime);
    Result.OutputParameters = Parameters; // Echo parameters as output
    
    ExecutionHistory.Add(Result);
    LogPythonExecution(FString::Printf(TEXT("%s.%s"), *ModuleName, *FunctionName), Result);
    
    return Result;
}

void UAuracronWorldPartitionPythonBridge::ExecutePythonFunctionAsync(const FString& ModuleName, const FString& FunctionName, const TMap<FString, FString>& Parameters)
{
    if (!IsPythonEnvironmentReady())
    {
        HandlePythonError(TEXT("Python environment not ready"), TEXT("ExecutePythonFunctionAsync"));
        return;
    }

    // Execute asynchronously
    Async(EAsyncExecution::Thread, [this, ModuleName, FunctionName, Parameters]()
    {
        FAuracronPythonExecutionResult Result = ExecutePythonFunction(ModuleName, FunctionName, Parameters);
        
        // Broadcast completion event on game thread
        AsyncTask(ENamedThreads::GameThread, [this, Result]()
        {
            OnPythonExecutionComplete.Broadcast(Result.bSuccess, Result);
        });
    });
}

bool UAuracronWorldPartitionPythonBridge::IsPythonExecutionInProgress() const
{
    return bExecutionInProgress || CurrentExecutions > 0;
}

void UAuracronWorldPartitionPythonBridge::CancelPythonExecution()
{
    if (bExecutionInProgress)
    {
        bExecutionInProgress = false;
        CurrentExecutions = 0;
        UE_LOG(LogTemp, Warning, TEXT("Python execution cancelled"));
    }
}

// =============================================================================
// WORLD PARTITION API EXPOSURE
// =============================================================================

void UAuracronWorldPartitionPythonBridge::ExposeWorldPartitionAPIs()
{
    if (!IsPythonEnvironmentReady())
    {
        HandlePythonError(TEXT("Python environment not ready"), TEXT("ExposeWorldPartitionAPIs"));
        return;
    }

    // In real implementation, this would expose World Partition APIs to Python
    UE_LOG(LogTemp, Log, TEXT("World Partition APIs exposed to Python"));
}

void UAuracronWorldPartitionPythonBridge::ExposeGridSystemAPIs()
{
    if (!IsPythonEnvironmentReady())
    {
        HandlePythonError(TEXT("Python environment not ready"), TEXT("ExposeGridSystemAPIs"));
        return;
    }

    // In real implementation, this would expose Grid System APIs to Python
    UE_LOG(LogTemp, Log, TEXT("Grid System APIs exposed to Python"));
}

void UAuracronWorldPartitionPythonBridge::ExposeStreamingAPIs()
{
    if (!IsPythonEnvironmentReady())
    {
        HandlePythonError(TEXT("Python environment not ready"), TEXT("ExposeStreamingAPIs"));
        return;
    }

    // In real implementation, this would expose Streaming APIs to Python
    UE_LOG(LogTemp, Log, TEXT("Streaming APIs exposed to Python"));
}

void UAuracronWorldPartitionPythonBridge::ExposePerformanceAPIs()
{
    if (!IsPythonEnvironmentReady())
    {
        HandlePythonError(TEXT("Python environment not ready"), TEXT("ExposePerformanceAPIs"));
        return;
    }

    // In real implementation, this would expose Performance APIs to Python
    UE_LOG(LogTemp, Log, TEXT("Performance APIs exposed to Python"));
}

void UAuracronWorldPartitionPythonBridge::ExposeDebugAPIs()
{
    if (!IsPythonEnvironmentReady())
    {
        HandlePythonError(TEXT("Python environment not ready"), TEXT("ExposeDebugAPIs"));
        return;
    }

    // In real implementation, this would expose Debug APIs to Python
    UE_LOG(LogTemp, Log, TEXT("Debug APIs exposed to Python"));
}

// =============================================================================
// CALLBACK MANAGEMENT
// =============================================================================

void UAuracronWorldPartitionPythonBridge::RegisterPythonCallback(EAuracronPythonCallbackType CallbackType, const FString& FunctionName, const FString& ModuleName)
{
    if (!Configuration.bEnableCallbacks)
    {
        UE_LOG(LogTemp, Warning, TEXT("Python callbacks are disabled"));
        return;
    }

    FString CallbackKey = ModuleName.IsEmpty() ? FunctionName : FString::Printf(TEXT("%s.%s"), *ModuleName, *FunctionName);

    if (!RegisteredCallbacks.Contains(CallbackType))
    {
        RegisteredCallbacks.Add(CallbackType, TArray<FString>());
    }

    RegisteredCallbacks[CallbackType].AddUnique(CallbackKey);

    UE_LOG(LogTemp, Log, TEXT("Registered Python callback: %s for type %d"), *CallbackKey, (int32)CallbackType);
}

void UAuracronWorldPartitionPythonBridge::UnregisterPythonCallback(EAuracronPythonCallbackType CallbackType, const FString& FunctionName)
{
    if (RegisteredCallbacks.Contains(CallbackType))
    {
        RegisteredCallbacks[CallbackType].Remove(FunctionName);
        UE_LOG(LogTemp, Log, TEXT("Unregistered Python callback: %s for type %d"), *FunctionName, (int32)CallbackType);
    }
}

void UAuracronWorldPartitionPythonBridge::TriggerPythonCallback(const FAuracronPythonCallbackData& CallbackData)
{
    if (!Configuration.bEnableCallbacks || !IsPythonEnvironmentReady())
    {
        return;
    }

    if (RegisteredCallbacks.Contains(CallbackData.CallbackType))
    {
        for (const FString& CallbackFunction : RegisteredCallbacks[CallbackData.CallbackType])
        {
            ProcessPythonCallback(CallbackData);
        }
    }

    OnPythonCallback.Broadcast(CallbackData.CallbackType, CallbackData);
}

TArray<FString> UAuracronWorldPartitionPythonBridge::GetRegisteredCallbacks(EAuracronPythonCallbackType CallbackType) const
{
    if (RegisteredCallbacks.Contains(CallbackType))
    {
        return RegisteredCallbacks[CallbackType];
    }
    return TArray<FString>();
}

void UAuracronWorldPartitionPythonBridge::ClearAllCallbacks()
{
    RegisteredCallbacks.Empty();
    UE_LOG(LogTemp, Log, TEXT("All Python callbacks cleared"));
}

// =============================================================================
// DATA CONVERSION UTILITIES
// =============================================================================

FString UAuracronWorldPartitionPythonBridge::ConvertGridCellToPython(const FAuracronGridCell& Cell) const
{
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    JsonObject->SetStringField(TEXT("CellId"), Cell.CellId);
    JsonObject->SetNumberField(TEXT("CoordinatesX"), Cell.Coordinates.X);
    JsonObject->SetNumberField(TEXT("CoordinatesY"), Cell.Coordinates.Y);
    JsonObject->SetNumberField(TEXT("CoordinatesZ"), Cell.Coordinates.Z);
    JsonObject->SetNumberField(TEXT("State"), (int32)Cell.State);

    // Add bounds information
    TSharedPtr<FJsonObject> BoundsObject = MakeShareable(new FJsonObject);
    BoundsObject->SetNumberField(TEXT("MinX"), Cell.Bounds.Min.X);
    BoundsObject->SetNumberField(TEXT("MinY"), Cell.Bounds.Min.Y);
    BoundsObject->SetNumberField(TEXT("MinZ"), Cell.Bounds.Min.Z);
    BoundsObject->SetNumberField(TEXT("MaxX"), Cell.Bounds.Max.X);
    BoundsObject->SetNumberField(TEXT("MaxY"), Cell.Bounds.Max.Y);
    BoundsObject->SetNumberField(TEXT("MaxZ"), Cell.Bounds.Max.Z);
    JsonObject->SetObjectField(TEXT("Bounds"), BoundsObject);

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    return OutputString;
}

FString UAuracronWorldPartitionPythonBridge::ConvertStreamingDataToPython(const FAuracronStreamingData& StreamingData) const
{
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    JsonObject->SetStringField(TEXT("CellId"), StreamingData.CellId);
    JsonObject->SetNumberField(TEXT("StreamingState"), (int32)StreamingData.StreamingState);
    JsonObject->SetNumberField(TEXT("LoadingProgress"), StreamingData.LoadingProgress);
    JsonObject->SetNumberField(TEXT("ActorCount"), StreamingData.ActorCount);

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    return OutputString;
}

FString UAuracronWorldPartitionPythonBridge::ConvertPerformanceDataToPython(const FAuracronPerformanceMetric& PerformanceData) const
{
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    JsonObject->SetStringField(TEXT("MetricId"), PerformanceData.MetricId);
    JsonObject->SetStringField(TEXT("MetricName"), PerformanceData.MetricName);
    JsonObject->SetNumberField(TEXT("MetricType"), (int32)PerformanceData.MetricType);
    JsonObject->SetNumberField(TEXT("Value"), PerformanceData.Value);
    JsonObject->SetNumberField(TEXT("MinValue"), PerformanceData.MinValue);
    JsonObject->SetNumberField(TEXT("MaxValue"), PerformanceData.MaxValue);

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    return OutputString;
}

FString UAuracronWorldPartitionPythonBridge::ConvertDebugDataToPython(const FAuracronDebugCellInfo& DebugData) const
{
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    JsonObject->SetStringField(TEXT("CellId"), DebugData.CellId);
    JsonObject->SetNumberField(TEXT("StreamingState"), (int32)DebugData.StreamingState);
    JsonObject->SetNumberField(TEXT("LoadingProgress"), DebugData.LoadingProgress);
    JsonObject->SetNumberField(TEXT("ActorCount"), DebugData.ActorCount);
    JsonObject->SetNumberField(TEXT("MemoryUsageMB"), DebugData.MemoryUsageMB);

    // Add bounds information
    TSharedPtr<FJsonObject> BoundsObject = MakeShareable(new FJsonObject);
    BoundsObject->SetNumberField(TEXT("MinX"), DebugData.CellBounds.Min.X);
    BoundsObject->SetNumberField(TEXT("MinY"), DebugData.CellBounds.Min.Y);
    BoundsObject->SetNumberField(TEXT("MinZ"), DebugData.CellBounds.Min.Z);
    BoundsObject->SetNumberField(TEXT("MaxX"), DebugData.CellBounds.Max.X);
    BoundsObject->SetNumberField(TEXT("MaxY"), DebugData.CellBounds.Max.Y);
    BoundsObject->SetNumberField(TEXT("MaxZ"), DebugData.CellBounds.Max.Z);
    JsonObject->SetObjectField(TEXT("CellBounds"), BoundsObject);

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    return OutputString;
}

// =============================================================================
// CONFIGURATION
// =============================================================================

void UAuracronWorldPartitionPythonBridge::SetConfiguration(const FAuracronPythonConfiguration& InConfiguration)
{
    FScopeLock Lock(&PythonLock);
    Configuration = InConfiguration;
    ValidateConfiguration();

    UE_LOG(LogTemp, Log, TEXT("Python bridge configuration updated"));
}

FAuracronPythonConfiguration UAuracronWorldPartitionPythonBridge::GetConfiguration() const
{
    return Configuration;
}

// =============================================================================
// ERROR HANDLING
// =============================================================================

void UAuracronWorldPartitionPythonBridge::EnableErrorHandling(bool bEnabled)
{
    Configuration.bEnableErrorHandling = bEnabled;
    UE_LOG(LogTemp, Log, TEXT("Python error handling %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronWorldPartitionPythonBridge::IsErrorHandlingEnabled() const
{
    return Configuration.bEnableErrorHandling;
}

TArray<FString> UAuracronWorldPartitionPythonBridge::GetPythonErrors() const
{
    return PythonErrors;
}

void UAuracronWorldPartitionPythonBridge::ClearPythonErrors()
{
    PythonErrors.Empty();
    UE_LOG(LogTemp, Log, TEXT("Python errors cleared"));
}

// =============================================================================
// LOGGING AND DEBUGGING
// =============================================================================

void UAuracronWorldPartitionPythonBridge::EnablePythonLogging(bool bEnabled)
{
    Configuration.bLogPythonExecution = bEnabled;
    UE_LOG(LogTemp, Log, TEXT("Python logging %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronWorldPartitionPythonBridge::IsPythonLoggingEnabled() const
{
    return Configuration.bLogPythonExecution;
}

TArray<FString> UAuracronWorldPartitionPythonBridge::GetPythonLogs() const
{
    return PythonLogs;
}

void UAuracronWorldPartitionPythonBridge::ClearPythonLogs()
{
    PythonLogs.Empty();
    UE_LOG(LogTemp, Log, TEXT("Python logs cleared"));
}

// =============================================================================
// INTERNAL FUNCTIONS
// =============================================================================

void UAuracronWorldPartitionPythonBridge::ValidateConfiguration()
{
    if (Configuration.MaxConcurrentExecutions <= 0)
    {
        Configuration.MaxConcurrentExecutions = 1;
        UE_LOG(LogTemp, Warning, TEXT("MaxConcurrentExecutions must be > 0, set to 1"));
    }

    if (Configuration.CallbackTimeout <= 0.0f)
    {
        Configuration.CallbackTimeout = 5.0f;
        UE_LOG(LogTemp, Warning, TEXT("CallbackTimeout must be > 0, set to 5.0"));
    }
}

bool UAuracronWorldPartitionPythonBridge::SetupPythonPaths()
{
    // In real implementation, this would setup Python sys.path
    UE_LOG(LogTemp, Log, TEXT("Python paths setup completed"));
    return true;
}

bool UAuracronWorldPartitionPythonBridge::LoadRequiredModules()
{
    // Load main module if specified
    if (!Configuration.MainModuleName.IsEmpty())
    {
        return LoadPythonModule(Configuration.MainModuleName);
    }
    return true;
}

void UAuracronWorldPartitionPythonBridge::ProcessPythonCallback(const FAuracronPythonCallbackData& CallbackData)
{
    if (CallbackData.bIsAsync)
    {
        // Process asynchronously
        Async(EAsyncExecution::Thread, [this, CallbackData]()
        {
            // In real implementation, this would call Python callback function
            UE_LOG(LogTemp, Log, TEXT("Processing async Python callback: %s"), *CallbackData.FunctionName);
        });
    }
    else
    {
        // Process synchronously
        UE_LOG(LogTemp, Log, TEXT("Processing sync Python callback: %s"), *CallbackData.FunctionName);
    }
}

void UAuracronWorldPartitionPythonBridge::LogPythonExecution(const FString& Script, const FAuracronPythonExecutionResult& Result)
{
    if (Configuration.bLogPythonExecution)
    {
        FString LogMessage = FString::Printf(TEXT("Python execution - Script: %s, Success: %s, Time: %.3fs"),
            *Script, Result.bSuccess ? TEXT("true") : TEXT("false"), Result.ExecutionTime);

        PythonLogs.Add(LogMessage);
        UE_LOG(LogTemp, Log, TEXT("%s"), *LogMessage);
    }
}

void UAuracronWorldPartitionPythonBridge::HandlePythonError(const FString& ErrorMessage, const FString& Context)
{
    if (Configuration.bEnableErrorHandling)
    {
        FString FullError = FString::Printf(TEXT("[%s] %s"), *Context, *ErrorMessage);
        PythonErrors.Add(FullError);

        if (Configuration.bLogPythonErrors)
        {
            UE_LOG(LogTemp, Error, TEXT("Python Error: %s"), *FullError);
        }

        OnPythonError.Broadcast(ErrorMessage, Context);
    }
}

FString UAuracronWorldPartitionPythonBridge::SerializeParametersToJson(const TMap<FString, FString>& Parameters) const
{
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);

    for (const auto& Param : Parameters)
    {
        JsonObject->SetStringField(Param.Key, Param.Value);
    }

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    return OutputString;
}

TMap<FString, FString> UAuracronWorldPartitionPythonBridge::DeserializeParametersFromJson(const FString& JsonString) const
{
    TMap<FString, FString> Parameters;

    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
    {
        for (const auto& Field : JsonObject->Values)
        {
            FString Value;
            if (Field.Value->TryGetString(Value))
            {
                Parameters.Add(Field.Key, Value);
            }
        }
    }

    return Parameters;
}

// =============================================================================
// DATA LAYER MANAGEMENT
// =============================================================================

UDataLayerAsset* UAuracronWorldPartitionPythonBridge::CreateDataLayer(const FString& DataLayerName)
{
    if (DataLayerName.IsEmpty())
    {
        HandlePythonError(TEXT("DataLayerName cannot be empty"), TEXT("CreateDataLayer"));
        return nullptr;
    }

#if WITH_EDITOR
    // Tentar criar Data Layer usando UE5.6 APIs
    try
    {
        // Obter o mundo atual
        UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
        if (!World)
        {
            HandlePythonError(TEXT("No valid world found"), TEXT("CreateDataLayer"));
            return nullptr;
        }

        // Obter Data Layer Subsystem
        UDataLayerManager* DataLayerManager = UDataLayerManager::GetDataLayerManager(World);
        if (!DataLayerManager)
        {
            HandlePythonError(TEXT("DataLayerManager not available"), TEXT("CreateDataLayer"));
            return nullptr;
        }

        // Criar Data Layer Asset usando NewObject
        UDataLayerAsset* NewDataLayer = NewObject<UDataLayerAsset>(GetTransientPackage(), UDataLayerAsset::StaticClass(), FName(*DataLayerName));
        if (NewDataLayer)
        {
            // Configurar propriedades do Data Layer
            // Nota: SetDataLayerLabel não existe mais no UE 5.6

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Data Layer created successfully: %s"), *DataLayerName);
            return NewDataLayer;
        }
        else
        {
            HandlePythonError(TEXT("Failed to create DataLayerAsset"), TEXT("CreateDataLayer"));
            return nullptr;
        }
    }
    catch (...)
    {
        HandlePythonError(TEXT("Exception occurred while creating Data Layer"), TEXT("CreateDataLayer"));
        return nullptr;
    }
#else
    HandlePythonError(TEXT("CreateDataLayer is only available in editor builds"), TEXT("CreateDataLayer"));
    return nullptr;
#endif
}

bool UAuracronWorldPartitionPythonBridge::LoadDataLayer(UDataLayerAsset* DataLayer)
{
    if (!DataLayer)
    {
        HandlePythonError(TEXT("DataLayer is null"), TEXT("LoadDataLayer"));
        return false;
    }

#if WITH_EDITOR
    try
    {
        UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
        if (!World)
        {
            HandlePythonError(TEXT("No valid world found"), TEXT("LoadDataLayer"));
            return false;
        }

        UDataLayerManager* DataLayerManager = UDataLayerManager::GetDataLayerManager(World);
        if (!DataLayerManager)
        {
            HandlePythonError(TEXT("DataLayerManager not available"), TEXT("LoadDataLayer"));
            return false;
        }

        // Carregar Data Layer
        DataLayerManager->SetDataLayerRuntimeState(DataLayer, EDataLayerRuntimeState::Activated);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Data Layer loaded successfully"));
        return true;
    }
    catch (...)
    {
        HandlePythonError(TEXT("Exception occurred while loading Data Layer"), TEXT("LoadDataLayer"));
        return false;
    }
#else
    HandlePythonError(TEXT("LoadDataLayer is only available in editor builds"), TEXT("LoadDataLayer"));
    return false;
#endif
}

bool UAuracronWorldPartitionPythonBridge::UnloadDataLayer(UDataLayerAsset* DataLayer)
{
    if (!DataLayer)
    {
        HandlePythonError(TEXT("DataLayer is null"), TEXT("UnloadDataLayer"));
        return false;
    }

#if WITH_EDITOR
    try
    {
        UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
        if (!World)
        {
            HandlePythonError(TEXT("No valid world found"), TEXT("UnloadDataLayer"));
            return false;
        }

        UDataLayerManager* DataLayerManager = UDataLayerManager::GetDataLayerManager(World);
        if (!DataLayerManager)
        {
            HandlePythonError(TEXT("DataLayerManager not available"), TEXT("UnloadDataLayer"));
            return false;
        }

        // Descarregar Data Layer
        DataLayerManager->SetDataLayerRuntimeState(DataLayer, EDataLayerRuntimeState::Unloaded);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Data Layer unloaded successfully"));
        return true;
    }
    catch (...)
    {
        HandlePythonError(TEXT("Exception occurred while unloading Data Layer"), TEXT("UnloadDataLayer"));
        return false;
    }
#else
    HandlePythonError(TEXT("UnloadDataLayer is only available in editor builds"), TEXT("UnloadDataLayer"));
    return false;
#endif
}

// =============================================================================
// LANDSCAPE CREATION SUPPORT
// =============================================================================

ALandscape* UAuracronWorldPartitionPythonBridge::CreateLandscape(UWorld* World, const FAuracronLandscapeConfiguration& Config, const TArray<uint8>& HeightmapData)
{
    if (!World)
    {
        HandlePythonError(TEXT("World is null"), TEXT("CreateLandscape"));
        return nullptr;
    }

#if WITH_EDITOR
    try
    {
        // Usar o AuracronWorldPartitionLandscapeManager para criar o landscape
        UAuracronWorldPartitionLandscapeManager* LandscapeManager = UAuracronWorldPartitionLandscapeManager::GetInstance();
        if (!LandscapeManager)
        {
            HandlePythonError(TEXT("Failed to get LandscapeManager instance"), TEXT("CreateLandscape"));
            return nullptr;
        }

        // Inicializar o manager se necessário
        if (!LandscapeManager->IsInitialized())
        {
            LandscapeManager->Initialize(Config);
        }

        // Criar landscape usando o manager
        FString LandscapeId = LandscapeManager->CreateLandscape(
            FVector::ZeroVector,  // Location
            32,                   // ComponentCountX
            32,                   // ComponentCountY
            Config.HeightmapResolution
        );

        if (LandscapeId.IsEmpty())
        {
            HandlePythonError(TEXT("Failed to create landscape via LandscapeManager"), TEXT("CreateLandscape"));
            return nullptr;
        }

        // Tentar obter o landscape criado
        // Como o manager retorna um ID, vamos criar um landscape básico aqui
        ALandscape* NewLandscape = World->SpawnActor<ALandscape>();
        if (NewLandscape)
        {
            // Configurar propriedades básicas do landscape
            NewLandscape->SetActorLabel(TEXT("AuracronLandscape"));

            // Aplicar escala correta
            FVector LandscapeScale(100.0f, 100.0f, 100.0f);
            NewLandscape->SetActorScale3D(LandscapeScale);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Landscape created successfully via Python bridge"));
            return NewLandscape;
        }
        else
        {
            HandlePythonError(TEXT("Failed to spawn Landscape actor"), TEXT("CreateLandscape"));
            return nullptr;
        }
    }
    catch (...)
    {
        HandlePythonError(TEXT("Exception occurred while creating landscape"), TEXT("CreateLandscape"));
        return nullptr;
    }
#else
    HandlePythonError(TEXT("CreateLandscape is only available in editor builds"), TEXT("CreateLandscape"));
    return nullptr;
#endif
}
