// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Python Integration Header
// Bridge 3.13: World Partition - Python Integration

#pragma once

#include "CoreMinimal.h"
#include "AuracronWorldPartitionBridgeAPI.h"
#include "UObject/NoExportTypes.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionGrid.h"
#include "AuracronWorldPartitionStreaming.h"
#include "AuracronWorldPartitionPerformance.h"
#include "AuracronWorldPartitionDebug.h"

// Python integration includes for UE5.6
#include "Engine/Engine.h"
#include "Modules/ModuleManager.h"
#include "HAL/PlatformProcess.h"
#include "Misc/Paths.h"

// Forward declarations
class UAuracronWorldPartitionPythonBridge;

#include "AuracronWorldPartitionPython.generated.h"

// =============================================================================
// PYTHON INTEGRATION TYPES
// =============================================================================

// Python callback types
UENUM(BlueprintType)
enum class EAuracronPythonCallbackType : uint8
{
    CellLoaded              UMETA(DisplayName = "Cell Loaded"),
    CellUnloaded            UMETA(DisplayName = "Cell Unloaded"),
    StreamingStarted        UMETA(DisplayName = "Streaming Started"),
    StreamingCompleted      UMETA(DisplayName = "Streaming Completed"),
    PerformanceAlert        UMETA(DisplayName = "Performance Alert"),
    DebugEvent              UMETA(DisplayName = "Debug Event"),
    GridUpdated             UMETA(DisplayName = "Grid Updated"),
    LayerChanged            UMETA(DisplayName = "Layer Changed")
};

// Python execution modes
UENUM(BlueprintType)
enum class EAuracronPythonExecutionMode : uint8
{
    Synchronous             UMETA(DisplayName = "Synchronous"),
    Asynchronous            UMETA(DisplayName = "Asynchronous"),
    Threaded                UMETA(DisplayName = "Threaded"),
    Deferred                UMETA(DisplayName = "Deferred")
};

// =============================================================================
// PYTHON CONFIGURATION
// =============================================================================

/**
 * Python Configuration
 * Configuration settings for Python integration
 */
USTRUCT(BlueprintType)
struct FAuracronPythonConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python")
    bool bEnablePythonIntegration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python")
    bool bEnableAsyncExecution = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python")
    bool bEnableCallbacks = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python")
    bool bEnableErrorHandling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python")
    EAuracronPythonExecutionMode DefaultExecutionMode = EAuracronPythonExecutionMode::Asynchronous;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python")
    FString PythonModulePath = TEXT("Scripts/Python/PCG");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python")
    FString MainModuleName = TEXT("AuracronPCGManager");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python")
    float CallbackTimeout = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python")
    int32 MaxConcurrentExecutions = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python")
    bool bLogPythonExecution = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python")
    bool bLogPythonErrors = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python")
    bool bAutoReloadModules = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python")
    TArray<FString> AdditionalPythonPaths;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python")
    TMap<FString, FString> PythonEnvironmentVariables;

    FAuracronPythonConfiguration()
    {
        bEnablePythonIntegration = true;
        bEnableAsyncExecution = true;
        bEnableCallbacks = true;
        bEnableErrorHandling = true;
        DefaultExecutionMode = EAuracronPythonExecutionMode::Asynchronous;
        PythonModulePath = TEXT("Scripts/Python/PCG");
        MainModuleName = TEXT("AuracronPCGManager");
        CallbackTimeout = 5.0f;
        MaxConcurrentExecutions = 4;
        bLogPythonExecution = true;
        bLogPythonErrors = true;
        bAutoReloadModules = false;
    }
};

// =============================================================================
// PYTHON CALLBACK DATA
// =============================================================================

/**
 * Python Callback Data
 * Data structure for Python callbacks
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronPythonCallbackData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python Callback")
    EAuracronPythonCallbackType CallbackType = EAuracronPythonCallbackType::CellLoaded;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python Callback")
    FString CallbackId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python Callback")
    FString FunctionName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python Callback")
    TMap<FString, FString> Parameters;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python Callback")
    FDateTime Timestamp;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python Callback")
    bool bIsAsync = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python Callback")
    float Priority = 1.0f;

    FAuracronPythonCallbackData()
    {
        CallbackType = EAuracronPythonCallbackType::CellLoaded;
        Timestamp = FDateTime::Now();
        bIsAsync = false;
        Priority = 1.0f;
    }
};

// =============================================================================
// PYTHON EXECUTION RESULT
// =============================================================================

/**
 * Python Execution Result
 * Result data from Python script execution
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronPythonExecutionResult
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python Result")
    bool bSuccess = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python Result")
    FString ResultData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python Result")
    FString ErrorMessage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python Result")
    float ExecutionTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python Result")
    FDateTime ExecutionTimestamp;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Python Result")
    TMap<FString, FString> OutputParameters;

    FAuracronPythonExecutionResult()
    {
        bSuccess = false;
        ExecutionTime = 0.0f;
        ExecutionTimestamp = FDateTime::Now();
    }
};

/**
 * Streaming Data
 * Data structure for streaming information
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronStreamingData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming Data")
    FString CellId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming Data")
    EAuracronCellStreamingState StreamingState = EAuracronCellStreamingState::Unloaded;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming Data")
    float LoadingProgress = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming Data")
    FBox CellBounds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming Data")
    int32 ActorCount = 0;

    FAuracronStreamingData()
    {
        StreamingState = EAuracronCellStreamingState::Unloaded;
        LoadingProgress = 0.0f;
        CellBounds = FBox(ForceInit);
        ActorCount = 0;
    }
};

// =============================================================================
// WORLD PARTITION PYTHON BRIDGE
// =============================================================================

/**
 * World Partition Python Bridge
 * Main bridge class for Python integration with World Partition system
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONWORLDPARTITIONBRIDGE_API UAuracronWorldPartitionPythonBridge : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    static UAuracronWorldPartitionPythonBridge* GetInstance();

    // Bridge lifecycle
    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    void Initialize(const FAuracronPythonConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    bool IsInitialized() const;

    // Python environment management
    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    bool InitializePythonEnvironment();

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    void ShutdownPythonEnvironment();

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    bool IsPythonEnvironmentReady() const;

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    bool LoadPythonModule(const FString& ModuleName);

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    bool ReloadPythonModule(const FString& ModuleName);

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    TArray<FString> GetLoadedModules() const;

    // Python script execution
    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    FAuracronPythonExecutionResult ExecutePythonScript(const FString& Script, EAuracronPythonExecutionMode ExecutionMode = EAuracronPythonExecutionMode::Synchronous);

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    FAuracronPythonExecutionResult ExecutePythonFunction(const FString& ModuleName, const FString& FunctionName, const TMap<FString, FString>& Parameters);

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    void ExecutePythonFunctionAsync(const FString& ModuleName, const FString& FunctionName, const TMap<FString, FString>& Parameters);

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    bool IsPythonExecutionInProgress() const;

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    void CancelPythonExecution();

    // World Partition API exposure
    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    void ExposeWorldPartitionAPIs();

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    void ExposeGridSystemAPIs();

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    void ExposeStreamingAPIs();

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    void ExposePerformanceAPIs();

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    void ExposeDebugAPIs();

    // Data Layer management
    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    class UDataLayerAsset* CreateDataLayer(const FString& DataLayerName);

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    bool LoadDataLayer(class UDataLayerAsset* DataLayer);

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    bool UnloadDataLayer(class UDataLayerAsset* DataLayer);

    // Landscape creation support
    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    class ALandscape* CreateLandscape(class UWorld* World, const FAuracronLandscapeConfiguration& Config, const TArray<uint8>& HeightmapData);

    // Callback management
    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    void RegisterPythonCallback(EAuracronPythonCallbackType CallbackType, const FString& FunctionName, const FString& ModuleName = TEXT(""));

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    void UnregisterPythonCallback(EAuracronPythonCallbackType CallbackType, const FString& FunctionName);

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    void TriggerPythonCallback(const FAuracronPythonCallbackData& CallbackData);

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    TArray<FString> GetRegisteredCallbacks(EAuracronPythonCallbackType CallbackType) const;

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    void ClearAllCallbacks();

    // Data conversion utilities
    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    FString ConvertGridCellToPython(const FAuracronGridCell& Cell) const;

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    FString ConvertStreamingDataToPython(const FAuracronStreamingData& StreamingData) const;

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    FString ConvertPerformanceDataToPython(const FAuracronPerformanceMetric& PerformanceData) const;

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    FString ConvertDebugDataToPython(const FAuracronDebugCellInfo& DebugData) const;

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    void SetConfiguration(const FAuracronPythonConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    FAuracronPythonConfiguration GetConfiguration() const;

    // Error handling
    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    void EnableErrorHandling(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    bool IsErrorHandlingEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    TArray<FString> GetPythonErrors() const;

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    void ClearPythonErrors();

    // Logging and debugging
    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    void EnablePythonLogging(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    bool IsPythonLoggingEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    TArray<FString> GetPythonLogs() const;

    UFUNCTION(BlueprintCallable, Category = "Python Bridge")
    void ClearPythonLogs();

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPythonExecutionComplete, bool, bSuccess, FAuracronPythonExecutionResult, Result);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPythonError, FString, ErrorMessage, FString, Context);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPythonCallback, EAuracronPythonCallbackType, CallbackType, FAuracronPythonCallbackData, CallbackData);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPythonExecutionComplete OnPythonExecutionComplete;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPythonError OnPythonError;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPythonCallback OnPythonCallback;

private:
    static UAuracronWorldPartitionPythonBridge* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronPythonConfiguration Configuration;

    UPROPERTY()
    bool bPythonEnvironmentReady = false;

    // Python state
    TArray<FString> LoadedModules;
    TMap<EAuracronPythonCallbackType, TArray<FString>> RegisteredCallbacks;
    TArray<FString> PythonErrors;
    TArray<FString> PythonLogs;
    TArray<FAuracronPythonExecutionResult> ExecutionHistory;

    // Execution state
    bool bExecutionInProgress = false;
    int32 CurrentExecutions = 0;

    // Thread safety
    mutable FCriticalSection PythonLock;

    // Internal functions
    void ValidateConfiguration();
    bool SetupPythonPaths();
    bool LoadRequiredModules();
    void ProcessPythonCallback(const FAuracronPythonCallbackData& CallbackData);
    void LogPythonExecution(const FString& Script, const FAuracronPythonExecutionResult& Result);
    void HandlePythonError(const FString& ErrorMessage, const FString& Context);
    FString SerializeParametersToJson(const TMap<FString, FString>& Parameters) const;
    TMap<FString, FString> DeserializeParametersFromJson(const FString& JsonString) const;
};
